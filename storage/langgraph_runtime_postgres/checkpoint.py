from __future__ import annotations

import logging
import os
import typing
import uuid

from langgraph.checkpoint.postgres import PostgresSaver

if typing.TYPE_CHECKING:
    from langchain_core.runnables import RunnableConfig
    from langgraph.checkpoint.base import (
        Checkpoint,
        CheckpointMetadata,
        CheckpointTuple,
        SerializerProtocol,
    )

logger = logging.getLogger(__name__)

_EXCLUDED_KEYS = {"checkpoint_ns", "checkpoint_id", "run_id", "thread_id"}


class PostgresCheckpointer(PostgresSaver):
    def __init__(
        self,
        conn_string: str,
        *,
        serde: SerializerProtocol | None = None,
    ) -> None:
        from langgraph_api.serde import Serializer

        super().__init__(
            conn_string,
            serde=serde if serde is not None else Serializer(),
        )

    def put(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: dict[str, str | int | float],
    ) -> RunnableConfig:
        # TODO: Should this be done in OSS as well?
        metadata = {
            **{
                k: v
                for k, v in config["configurable"].items()
                if not k.startswith("__") and k not in _EXCLUDED_KEYS
            },
            **config.get("metadata", {}),
            **metadata,
        }
        if not isinstance(checkpoint["id"], uuid.UUID):
            # Avoid type inconsistencies
            checkpoint = checkpoint.copy()
            checkpoint["id"] = str(checkpoint["id"])
        return super().put(config, checkpoint, metadata, new_versions)

    def get_tuple(self, config: RunnableConfig) -> CheckpointTuple | None:
        if isinstance(config["configurable"].get("checkpoint_id"), uuid.UUID):
            # Avoid type inconsistencies....
            config = config.copy()

            config["configurable"] = {
                **config["configurable"],
                "checkpoint_id": str(config["configurable"]["checkpoint_id"]),
            }
        return super().get_tuple(config)


POSTGRES_CHECKPOINTER = None


def Checkpointer(*args, unpack_hook=None, **kwargs):
    global POSTGRES_CHECKPOINTER

    # Get database URI from environment
    database_uri = os.getenv("DATABASE_URI")
    if not database_uri:
        raise ValueError("DATABASE_URI environment variable is required for PostgreSQL checkpointer")

    if POSTGRES_CHECKPOINTER is None:
        POSTGRES_CHECKPOINTER = PostgresCheckpointer(database_uri)

    if unpack_hook is not None:
        from langgraph_api.serde import Serializer

        return PostgresCheckpointer(
            database_uri,
            serde=Serializer(__unpack_ext_hook__=unpack_hook),
            **kwargs
        )
    return POSTGRES_CHECKPOINTER


__all__ = ["Checkpointer"]
