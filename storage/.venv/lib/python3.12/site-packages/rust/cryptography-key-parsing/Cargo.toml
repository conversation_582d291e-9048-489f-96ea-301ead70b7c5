[package]
name = "cryptography-key-parsing"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true
rust-version.workspace = true

[dependencies]
asn1.workspace = true
cfg-if = "1"
openssl = "0.10.68"
openssl-sys = "0.9.104"
cryptography-x509 = { path = "../cryptography-x509" }

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ['cfg(CRYPTOGRAPHY_IS_LIBRESSL)', 'cfg(CRYPTOGRAPHY_IS_BORINGSSL)'] }
